<template>
  <!-- 搜索和步骤 -->
  <div class="step-search column">
    <!-- 标题和搜索 -->
    <h1 class="step-search-title">{{ $t('homeTitle') }}</h1>
    <div class="search-bar">
      <div class="search-input-container">
        <input type="search" v-model="searchText" :placeholder="$t('searchPlaceholder')" />
        <i class="iconfont icon-zhaoxiangji zhaoxiangji gt-xs" />
      </div>
      <div class="search-button-container" @click="onSubmit">
        <button class="search-button">
          <i class="iconfont icon-sousuo sousuo" />
        </button>
      </div>
    </div>

    <!-- 分步 - 桌面端 -->
    <div class="steps gt-xs">
      <!-- <div class="step">
        <div class="step-number">1</div>
        <p>{{ $t('step1') }}</p>
      </div>
      <div class="step-arrow">
        <i class="iconfont icon-youjiantou"></i>
      </div>
      <div class="step">
        <div class="step-number">2</div>
        <p>{{ $t('step2') }}</p>
      </div>
      <div class="step-arrow">
        <i class="iconfont icon-youjiantou"></i>
      </div>
      <div class="step">
        <div class="step-number">3</div>
        <p>{{ $t('step3') }}</p>
      </div>
      <div class="step-arrow">
        <i class="iconfont icon-youjiantou"></i>
      </div>
      <div class="step">
        <div class="step-number">4</div>
        <p>{{ $t('step4') }}</p>
      </div> -->

      <q-stepper v-model="step" ref="stepper" color="primary" flat header-nav style="min-width: 800px">
        <q-step :name="1" title="下单并付款" icon="fa-solid fa-circle-1" ">
          <!-- Step content -->
        </q-step>

        <q-step :name="2" title="平台采购" icon="fas fa-truck-loading" ">
          <!-- Step content -->
        </q-step>

        <q-step :name="3" title="提交发货" icon="fas fa-box-open" :done="step > 3">
          <!-- Step content -->
        </q-step>

        <q-step :name="4" title="收货" icon="fas fa-check-circle" :done="step > 4">
          <!-- Step content -->
        </q-step>

        <!-- <template v-slot:navigation>
          <q-stepper-navigation>
            <q-btn @click="$refs.stepper.next()" color="primary" label="继续" />
            <q-btn v-if="step > 1" flat color="primary" @click="$refs.stepper.previous()" label="返回" class="q-ml-sm" />
          </q-stepper-navigation>
        </template> -->
      </q-stepper>
    </div>

    <!-- 分步 - 移动端
    <div class="steps-mobile lt-sm">
      <q-carousel v-model="slide" animated swipeable arrows navigation padding height="120px" class="bg-transparent text-white shadow-1 rounded-borders">
        <q-carousel-slide :name="1" class="column no-wrap flex-center">
          <div class="step-mobile">
            <div class="step-number">1</div>
            <p>{{ $t('step1') }}</p>
          </div>
        </q-carousel-slide>
        <q-carousel-slide :name="2" class="column no-wrap flex-center">
          <div class="step-mobile">
            <div class="step-number">2</div>
            <p>{{ $t('step2') }}</p>
          </div>
        </q-carousel-slide>
        <q-carousel-slide :name="3" class="column no-wrap flex-center">
          <div class="step-mobile">
            <div class="step-number">3</div>
            <p>{{ $t('step3') }}</p>
          </div>
        </q-carousel-slide>
        <q-carousel-slide :name="4" class="column no-wrap flex-center">
          <div class="step-mobile">
            <div class="step-number">4</div>
            <p>{{ $t('step4') }}</p>
          </div>
        </q-carousel-slide>
      </q-carousel>
    </div> -->
  </div>
</template>

<script setup>
import { analyzeSearchInput, buildSearchUrl } from '~/utils/searchUtils';

const searchText = ref('');
const slide = ref(1);

const step = ref(10);

const onSubmit = () => {
  if (searchText.value.trim()) {
    // 分析搜索输入类型并构建相应的URL
    const analysis = analyzeSearchInput(searchText.value.trim());
    const searchUrl = buildSearchUrl(analysis);
    navigateTo(searchUrl);
  } else {
    navigateTo('/search');
  }
};
</script>

<style lang="scss" scoped>
//搜索和分步
.step-search {
  width: 100%;
  min-height: 800px;
  // max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  background-image: url('/images/bg.svg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  text-align: center;
  color: #fff;
  position: relative;

  .step-search-title {
    padding-top: 350px;
    margin-bottom: 20px;
    font-size: 32px;
    font-weight: bold;
  }

  .search-bar {
    position: relative;
    width: 100%;
    max-width: 800px;
    height: 60px;
    margin: 0 auto;
    display: flex;
    justify-content: center;

    .search-input-container {
      flex: 1;
      position: relative;
    }

    input[type='search'] {
      width: 100%;
      height: 60px;
      border-radius: 40px 0 0 40px;
      border: none;
      padding: 0 40px;
      font-size: 18px;
    }

    .zhaoxiangji {
      font-size: 40px;
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #707070;
      cursor: pointer;
    }

    .search-button-container {
      width: 60px;
      height: 60px;
    }

    .search-button {
      width: 100%;
      height: 100%;
      background-color: #0073e6;
      border-radius: 0 40px 40px 0;
      border: none;
      cursor: pointer;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .sousuo {
      font-size: 24px;
      color: #ffffff;
    }
  }

  .steps {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 1000px;
    margin: 35px auto 0;
    padding-bottom: 20px;

    .step {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #fff;
        color: #0073e6;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        margin-bottom: 8px;
      }

      p {
        font-size: 16px;
        margin: 0;
      }
    }

    .step-arrow {
      margin: 0 10px;

      .icon-youjiantou {
        font-size: 24px;
      }
    }
  }

  .steps-mobile {
    margin: 20px auto;
    width: 100%;
    max-width: 400px;

    .step-mobile {
      display: flex;
      flex-direction: column;
      align-items: center;

      .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #fff;
        color: #0073e6;
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: bold;
        font-size: 18px;
        margin-bottom: 10px;
      }

      p {
        font-size: 16px;
        margin: 0;
        text-align: center;
      }
    }
  }
}

@media (max-width: 599px) {
  .step-search {
    height: auto;
    padding-bottom: 20px;

    .step-search-title {
      font-size: 24px;
      padding-top: 15px;
      margin-bottom: 15px;
    }

    .search-bar {
      height: 36px;

      input[type='search'] {
        height: 36px;
        font-size: 14px;
        padding: 0 10px;
      }

      .search-button-container {
        width: 50px;
        height: 36px;
      }

      .sousuo {
        font-size: 20px;
      }
    }
  }
}

@media (min-width: 600px) and (max-width: 1023px) {
  .step-search {
    height: auto;
    padding-bottom: 30px;

    .steps {
      flex-wrap: wrap;

      .step {
        margin: 10px 0;
      }

      .step-arrow {
        transform: rotate(90deg);
        margin: 5px 0;
      }
    }
  }
}
</style>
