<template>
  <div class="menu-container">
    <div class="head-menu">
      <!-- 移动端菜单按钮 -->
      <q-btn flat dense round icon="menu" color="secondary" class="lt-md mobile-menu-btn" @click="mobileMenuOpen = true" />

      <NuxtLink to="/" class="logo-container">
        <img class="logo" src="/images/logo.svg" alt="" />
      </NuxtLink>

      <!-- 桌面端菜单 -->
      <div class="menu gt-sm">
        <ul>
          <li>
            <nuxt-link to="/" class="menu-link" active-class="active">{{ $t('home') }}</nuxt-link>
          </li>
          <!-- <li>
          <nuxt-link to="/products" class="menu-link">{{ $t('hotProducts') }}</nuxt-link>
        </li> -->
          <li>
            <nuxt-link to="/diy-order" class="menu-link">DIY订单</nuxt-link>
          </li>
          <li>
            <nuxt-link to="/transfer" class="menu-link">{{ $t('transport') }}</nuxt-link>
          </li>
          <!-- <li>
          <nuxt-link to="/help/category/guide" class="menu-link">{{ $t('guide') }}</nuxt-link>
        </li> -->

          <li>
            <nuxt-link to="/bulk" class="menu-link">{{ $t('bulkPurchase') }}</nuxt-link>
          </li>
          <li>
            <nuxt-link to="/notice" class="menu-link">{{ $t('notes') }}</nuxt-link>
          </li>
          <li>
            <nuxt-link to="/help" class="menu-link">{{ $t('helpCenter') }}</nuxt-link>
          </li>
          <!-- <li>
          <a href="javascript:void(0)" class="menu-link" @click="openChatInNewWindow">
            <q-icon name="headset_mic" size="18px" class="q-mr-xs" />
            {{ $t('help.contactSupport.button') }}
          </a>
        </li> -->
        </ul>
      </div>

      <!-- 移动端和平板端搜索按钮 -->
      <q-btn flat dense round icon="search" color="secondary" class="lt-md mobile-search-btn" @click="mobileSearchOpen = true" />

      <div class="menu-right">
        <div class="login" @mouseenter="menuVisible = true" @mouseleave="menuVisible = false">
          <ClientOnly>
            <q-icon class="q-mb-xs" name="account_circle" size="22px" color="secondary" />
            <template v-if="!isLoggedIn">
              <div class="inline-block q-mx-sm gt-xs">
                <nuxt-link to="/login">
                  {{ $t('login') }}
                </nuxt-link>
                /
                <nuxt-link to="/register">{{ $t('register') }}</nuxt-link>
              </div>
            </template>
            <template v-else>
              <div class="username text-primary q-ml-sm gt-xs">
                {{ userInfo.name }}
                <q-icon name="arrow_drop_down" size="28px" />
              </div>

              <div class="user-dropdown" v-show="menuVisible">
                <div @click="goToCenter">{{ $t('button.myCenter') }}</div>
                <div @click="goToOrders">{{ $t('button.myOrders') }}</div>
                <div @click="goToProfile">{{ $t('button.myMessage') }}</div>
                <div @click="logout">{{ $t('button.logout') }}</div>
              </div>
            </template>
          </ClientOnly>
        </div>
        <div class="cart">
          <nuxt-link to="/cart">
            <q-icon class="q-mb-xs" name="shopping_cart" size="20px" color="secondary" />
            <span class="gt-xs">{{ $t('cart') }}</span>
          </nuxt-link>
        </div>
      </div>

      <!-- 移动端菜单对话框 -->
      <q-dialog v-model="mobileMenuOpen" position="left" full-height>
        <q-card class="mobile-menu-dialog" style="width: 280px; max-width: 90vw">
          <q-card-section class="q-pa-md q-pb-none">
            <div class="row items-center justify-between">
              <div class="text-h6">{{ $t('menu') }}</div>
              <q-btn icon="close" flat round dense v-close-popup />
            </div>
          </q-card-section>
          <q-card-section class="q-pa-none">
            <q-list padding>
              <q-item clickable v-ripple tag="a" href="/" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="home" />
                </q-item-section>
                <q-item-section>{{ $t('home') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/collection/hot" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="whatshot" />
                </q-item-section>
                <q-item-section>{{ $t('hotProducts') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/diy-order" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="build" />
                </q-item-section>
                <q-item-section>DIY Order</q-item-section>
              </q-item>

              <q-item clickable v-ripple>
                <q-item-section avatar>
                  <q-icon name="local_shipping" />
                </q-item-section>
                <q-item-section>{{ $t('transport') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/guide" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="help" />
                </q-item-section>
                <q-item-section>{{ $t('guide') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/help" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="support" />
                </q-item-section>
                <q-item-section>{{ $t('helpCenter') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/bulk" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="shopping_bag" />
                </q-item-section>
                <q-item-section>{{ $t('bulkPurchase') }}</q-item-section>
              </q-item>

              <q-item clickable v-ripple tag="a" href="/news" @click="mobileMenuOpen = false">
                <q-item-section avatar>
                  <q-icon name="article" />
                </q-item-section>
                <q-item-section>{{ $t('notes') }}</q-item-section>
              </q-item>

              <q-item
                clickable
                v-ripple
                @click="
                  openChatInNewWindow();
                  mobileMenuOpen = false;
                ">
                <q-item-section avatar>
                  <q-icon name="headset_mic" />
                </q-item-section>
                <q-item-section>{{ $t('help.contactSupport.button') }}</q-item-section>
              </q-item>

              <q-separator class="q-my-md" />

              <template v-if="isLoggedIn">
                <q-item
                  clickable
                  v-ripple
                  @click="
                    goToCenter();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="person" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.myCenter') }}</q-item-section>
                </q-item>

                <q-item
                  clickable
                  v-ripple
                  @click="
                    goToOrders();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="receipt_long" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.myOrders') }}</q-item-section>
                </q-item>

                <q-item
                  clickable
                  v-ripple
                  @click="
                    goToProfile();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="mail" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.myMessage') }}</q-item-section>
                </q-item>

                <q-item
                  clickable
                  v-ripple
                  @click="
                    logout();
                    mobileMenuOpen = false;
                  ">
                  <q-item-section avatar>
                    <q-icon name="logout" />
                  </q-item-section>
                  <q-item-section>{{ $t('button.logout') }}</q-item-section>
                </q-item>
              </template>
              <template v-else>
                <q-item clickable v-ripple tag="a" href="/login" @click="mobileMenuOpen = false">
                  <q-item-section avatar>
                    <q-icon name="login" />
                  </q-item-section>
                  <q-item-section>{{ $t('login') }}</q-item-section>
                </q-item>

                <q-item clickable v-ripple tag="a" href="/register" @click="mobileMenuOpen = false">
                  <q-item-section avatar>
                    <q-icon name="person_add" />
                  </q-item-section>
                  <q-item-section>{{ $t('register') }}</q-item-section>
                </q-item>
              </template>
            </q-list>
          </q-card-section>
        </q-card>
      </q-dialog>

      <!-- 移动端搜索对话框 -->
      <q-dialog v-model="mobileSearchOpen" position="top">
        <q-card class="mobile-search-dialog q-pa-md" style="width: 90%; max-width: 500px">
          <q-input v-model="searchText" outlined dense placeholder="搜索商品..." class="full-width">
            <template #append>
              <q-icon name="search" />
            </template>
          </q-input>
          <div class="row justify-end q-mt-sm">
            <q-btn flat label="取消" color="grey" @click="mobileSearchOpen = false" />
            <q-btn flat label="搜索" color="primary" @click="doSearch" />
          </div>
        </q-card>
      </q-dialog>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useAuthStore } from '~/store/auth';
import { useUserStore } from '~/store/user';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';

const menuVisible = ref(false);
const mobileMenuOpen = ref(false);
const mobileSearchOpen = ref(false);
const searchText = ref('');
const router = useRouter();
const $q = useQuasar();

// 从 Pinia 获取用户信息和登录状态
const authStore = useAuthStore();
const userStore = useUserStore();
const isLoggedIn = computed(() => authStore.isLogin);
const userInfo = computed(() => userStore.userInfo);

// 跳转到我的订单
const goToCenter = () => {
  router.push('/account');
  menuVisible.value = false;
};

// 跳转到我的订单
const goToOrders = () => {
  router.push('/account/orders');
  menuVisible.value = false;
};

// 跳转到我的信息
const goToProfile = () => {
  router.push('/account/message');
  menuVisible.value = false;
};

// 退出登录
const logout = () => {
  //清空token
  console.log('logout-----------------------------------');
  authStore.clearToken();

  const tokenCookie = useCookie('token');
  const refreshTokenCookie = useCookie('refreshToken');
  tokenCookie.value = null;
  refreshTokenCookie.value = null;

  //清空购物车和心愿单
  // cartStore.clearCart(); todo
  // wishlistStore.clearWishlist(); todo
  router.replace('/');
};

// 搜索功能
const doSearch = async () => {
  if (searchText.value.trim()) {
    // 分析搜索输入类型并构建相应的URL
    const { analyzeSearchInput, buildSearchUrl } = await import('~/utils/searchUtils');
    const analysis = analyzeSearchInput(searchText.value.trim());
    const searchUrl = buildSearchUrl(analysis);

    router.push(searchUrl);
    mobileSearchOpen.value = false;
    searchText.value = '';
  }
};

// 在新窗口打开客服聊天页面
const openChatInNewWindow = () => {
  window.open('/chat', '_blank');
};

// 监听屏幕尺寸变化，在大屏幕上自动关闭移动端菜单
watch(
  () => $q.screen.name,
  (newVal) => {
    if (newVal === 'sm' || newVal === 'md' || newVal === 'lg' || newVal === 'xl') {
      mobileMenuOpen.value = false;
      mobileSearchOpen.value = false;
    }
  }
);
</script>

<style lang="scss" scoped>
.menu-container {
  width: 100%;
  background-color: #fff;
}
.head-menu {
  width: 100%;
  max-width: 1200px;
  height: 60px;
  margin: 0 auto;
  background-color: #fff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;

  @media (max-width: 599px) {
    height: 50px;
    padding: 0 10px;
  }

  .mobile-menu-btn {
    margin-right: 10px;
    width: 40px;
    height: 40px;

    @media (max-width: 599px) {
      width: 36px;
      height: 36px;
    }
  }

  .mobile-search-btn {
    margin-left: auto;
    margin-right: 10px;
    width: 40px;
    height: 40px;

    @media (max-width: 599px) {
      width: 36px;
      height: 36px;
    }
  }

  .logo-container {
    display: flex;
    align-items: center;

    @media (max-width: 599px) {
      flex: 1;
      justify-content: center;
    }
  }

  .logo {
    width: 260px;
    height: 60px;

    @media (max-width: 1023px) {
      width: 200px;
      height: 46px;
    }

    @media (max-width: 599px) {
      width: 160px;
      height: 37px;
    }
  }

  .menu {
    // width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    ul {
      display: flex;
      font-size: 16px;
      margin: 0;
      padding: 0;
      li {
        display: inline-block;
        line-height: 40px;
        margin: 0 15px;
        position: relative;

        .menu-link {
          display: block;
          cursor: pointer;
          color: inherit;
          text-decoration: none;
          position: relative;

          &:hover,
          &.active {
            color: #4f87ff;

            &:after {
              content: '';
              position: absolute;
              bottom: 0;
              left: 0;
              width: 100%;
              height: 3px;
              background-color: #4f87ff;
            }
          }
        }
      }
    }
  }

  .menu-right {
    height: 100%;
    display: flex;
    align-items: center;
    font-size: 16px;

    @media (max-width: 599px) {
      font-size: 14px;
    }

    .cart {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-left: 15px;

      a {
        display: flex;
        align-items: center;
      }
    }

    .login {
      height: 100%;
      margin-right: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;

      .username {
        display: flex;
        align-items: center;
      }

      .user-dropdown {
        position: absolute;
        top: 90%;
        right: 0;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
        min-width: 120px;
        z-index: 1000;

        div {
          padding: 0px 12px;
          height: 40px;
          line-height: 40px;
          font-size: 13px;
          color: #333;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background-color: #f0f0f0;
          }

          &.active {
            color: #d9534f;
          }
        }
      }
    }
  }

  // 移动端菜单对话框样式
  .mobile-menu-dialog {
    background-color: #fff;

    .q-item {
      min-height: 48px;
    }
  }

  // 移动端搜索对话框样式
  .mobile-search-dialog {
    background-color: #fff;
    border-radius: 8px;
  }
}
</style>
